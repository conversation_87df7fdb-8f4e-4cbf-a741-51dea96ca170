import * as multisig from '@sqds/multisig'
import { PublicKey } from '@solana/web3.js'
import TOS from '../tos.json' assert {type:'json'}
import { MULTISIG_ADDRESS, MULTISIG_ADDRESS_PUBKEY, SQUADS_PROGRAM_ID_V4_PUBKEY } from './constants.js'
import { connection, validateParams, handleError, getTokenAccounts, getPrice, sendTransaction, parsePermissions, calculateAssetWeights, parseTransfer } from './utils.js'

export const getBlockhash = async ctx => {
  try {
    const { blockhash } = await connection.getLatestBlockhash()
    ctx.body = { blockhash }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const getBalance = async ctx => {
  try {
    const error = validateParams(ctx.request.body, ['publicKey'])
    if (error) return handleError(ctx, error)

    const { publicKey } = ctx.request.body
    const balance = await connection.getBalance(new PublicKey(publicKey))
    ctx.body = { balance }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const getTos = async ctx => {
  try {
    ctx.body = TOS
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const getMultisigs = async ctx => {
  try {
    // 多签账户信息
    const { threshold, transactionIndex, members } = await multisig.accounts.Multisig.fromAccountAddress(connection, MULTISIG_ADDRESS_PUBKEY)

    // 金库信息
    const [vaultPda] = multisig.getVaultPda({ multisigPda: MULTISIG_ADDRESS_PUBKEY, index: 0, programId: SQUADS_PROGRAM_ID_V4_PUBKEY})
    const vaultBalance = await connection.getBalance(vaultPda)

    // 资产列表
    const assets = []
    const solPrice = await getPrice('SOL')

    // 资产列表 - SOL 资产
    if (vaultBalance > 0) {
      assets.push({
        symbol: 'sol',
        address: vaultPda.toBase58(),
        balance: vaultBalance / 1e9,
        price: solPrice,
        value: (vaultBalance / 1e9) * solPrice
      })
    }

    // 资产列表 - Token 资产
    (await getTokenAccounts(vaultPda)).forEach(({ name, address, mint, decimals, uiAmount, price, value }) => assets.push({
      symbol: name,
      address,
      mint, // 保留 mint 地址，Token 转账需要
      decimals, // 保留 decimals，Token 转账需要
      balance: uiAmount,
      price,
      value
    }))

    ctx.body = {
      multisigAccount: MULTISIG_ADDRESS,
      threshold,
      transactionIndex: Number(transactionIndex.toString()),
      solPrice,
      vault: {
        address: vaultPda.toBase58(),
        balance: vaultBalance,
        balanceSOL: vaultBalance / 1e9,
        assets: calculateAssetWeights(assets),
        totalValue: calculateAssetWeights(assets).reduce((sum, asset) => sum + asset.value, 0)
      },
      members: members.map(({ key, permissions: { mask } }) => {
        return {
          address: key.toBase58(),
          permissions: parsePermissions(mask),
        }
      }), // TODO 过滤只有 Proposer 权限的用户
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const createTransfer = async (ctx) => {
  try {
    const { signedTransaction } = ctx.request.body
    let requiredParams = ['signedTransaction']
    const error = validateParams(ctx.request.body, requiredParams)
    if (error) return handleError(ctx, error)

    const signature = await sendTransaction(signedTransaction)
    const result = await connection.confirmTransaction(signature)
    ctx.body = {
      signature,
      result
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const getTransactions = async (ctx) => {
  const transactions = []
  try {
    const { page = 1, pageSize = 5 } = ctx.request.body

    // 多签账户信息
    const { threshold, transactionIndex } = await multisig.accounts.Multisig.fromAccountAddress(connection, MULTISIG_ADDRESS_PUBKEY)

    // 分页
    const totalPages = Math.ceil(transactionIndex / pageSize)
    const offset = (page - 1) * pageSize
    const startIndex = Math.max(1, transactionIndex - offset)
    const endIndex = Math.max(1, startIndex - pageSize + 1)

    for (let i = startIndex; i >= endIndex; i--) {
      // proposal 数据
      const [proposalPda] = multisig.getProposalPda({ multisigPda: MULTISIG_ADDRESS_PUBKEY, transactionIndex: i, programId: SQUADS_PROGRAM_ID_V4_PUBKEY })
      const proposalAccount = await connection.getAccountInfo(proposalPda)
      const { approved, rejected, cancelled, status: { timestamp, __kind: status } } = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0]

      // transaction 数据
      const [transactionPda] = multisig.getTransactionPda({ multisigPda: MULTISIG_ADDRESS_PUBKEY, index: BigInt(i), programId: SQUADS_PROGRAM_ID_V4_PUBKEY })
      const transactionAccount = await connection.getAccountInfo(transactionPda)

      // TODO 交易数据有效性
      if (transactionAccount.data.length < 150) continue
      const { message, message: { instructions, accountKeys } } = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0]

      // 尝试解析所有指令，找到有效的转账指令
      let transferResult = { chain: 'unknown', amount: 0, from: 'unknown', to: 'unknown' }
      for (let instrIndex = 0; instrIndex < instructions.length; instrIndex++) {
        try {
          const instruction = instructions[instrIndex]
          // 简单处理：如果 programIdIndex 超出范围，跳过
          if (instruction.programIdIndex >= accountKeys.length) continue

          const programId = accountKeys[instruction.programIdIndex]
          const result = await parseTransfer(programId, instruction, message)

          // 如果找到有效的转账金额，使用这个结果
          if (result.amount > 0) {
            transferResult = result
            break
          }
        } catch (e) {
          // 忽略解析失败的指令，继续尝试下一个
          continue
        }
      }

      const { chain, amount, from, to } = transferResult

      // TODO 交易创建时间
      // const signatures = await connection.getSignaturesForAddress(transactionPda, { limit: 5 })
      // const { blockTime } = signatures[signatures.length - 1]

      transactions.push({
        transactionIndex: i,
        amount,
        chain,
        from,
        to,
        createdAt: '', //new Date(blockTime * 1000).toISOString(),
        updatedAt: new Date(timestamp * 1000).toISOString(),
        status,
        approvals: approved.length,
        threshold,
        votes: [
          ...approved.map(member => ({ member: member.toBase58(), vote: 'Approve' })),
          ...rejected.map(member => ({ member: member.toBase58(), vote: 'Reject' })),
          ...cancelled.map(member => ({ member: member.toBase58(), vote: 'Cancel' })),
        ]
      })
    }

    ctx.body = {
      transactions,
      pagination: {
        page,
        pageSize,
        total: Number(transactionIndex),
        totalPages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

// 构建执行指令
export const buildExecuteInstruction = async (ctx) => {
  try {
    const error = validateParams(ctx.request.body, ['transactionIndex', 'executorPublicKey'])
    if (error) return handleError(ctx, error)

    const { transactionIndex, executorPublicKey } = ctx.request.body

    const { instruction: { keys, programId, data } } = await multisig.instructions.vaultTransactionExecute({
      connection,
      multisigPda: MULTISIG_ADDRESS_PUBKEY,
      transactionIndex: BigInt(transactionIndex),
      member: new PublicKey(executorPublicKey),
      programId: SQUADS_PROGRAM_ID_V4_PUBKEY
    })

    ctx.body = {
      keys: keys.map(({ pubkey, isSigner, isWritable }) => ({pubkey: pubkey.toBase58(), isSigner, isWritable })),
      programId: programId.toBase58(),
      data: Array.from(data)
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

export const handleTransactionAction = async (ctx) => {
  try {
    const { action } = ctx.params
    const { transactionIndex, userPublicKey, signedTransaction } = ctx.request.body

    const error = validateParams(ctx.request.body, ['transactionIndex', 'userPublicKey', 'signedTransaction'])
    if (error) return handleError(ctx,  error)

    if (action === 'execute') {
      const { threshold, members } = await multisig.accounts.Multisig.fromAccountAddress(connection, MULTISIG_ADDRESS_PUBKEY)

      // verify proposal
      const [proposalPda] = multisig.getProposalPda({ multisigPda: MULTISIG_ADDRESS_PUBKEY, transactionIndex: BigInt(transactionIndex), programId: SQUADS_PROGRAM_ID_V4_PUBKEY })
      const proposalAccount = await connection.getAccountInfo(proposalPda)
      if (!proposalAccount) return handleError(ctx, `交易 #${transactionIndex} 不存在`)
      const { approved, status: { __kind: status } } = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0]

      // verify vote
      if (approved.length < threshold) return handleError(ctx, `交易投票不足，需要 ${threshold} 票，当前只有 ${approved.length} 票`)
      if (status === 'Executed') return handleError(ctx, `交易 #${transactionIndex} 已经执行过了`)
      if (status === 'Cancelled') return handleError(ctx, `交易 #${transactionIndex} 已被取消`)

      // verify members
      const isMember = members.some(member => member.key.equals(new PublicKey(userPublicKey)))
      if (!isMember) return handleError(ctx, '执行者不是多签成员')

      // verify transaction
      const [transactionPda] = multisig.getTransactionPda({ multisigPda: MULTISIG_ADDRESS_PUBKEY, index: BigInt(transactionIndex), programId: SQUADS_PROGRAM_ID_V4_PUBKEY })
      const transactionAccount = await connection.getAccountInfo(transactionPda)
      if (!transactionAccount) return handleError(ctx, `交易 #${transactionIndex} 没有对应的交易内容，无法执行`)
    }
    const signature = await sendTransaction(signedTransaction)
    const result = await connection.confirmTransaction(signature)
    ctx.body = {
      signature,
      result
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}

// 取消交易
export const cancelTransaction = async (ctx) => {
  try {
    const error = validateParams(ctx.request.body, ['signedTransaction'])
    if (error) return handleError(ctx, error)

    const { signedTransaction } = ctx.request.body

    const signature = await sendTransaction(signedTransaction)
    const result = await connection.confirmTransaction(signature, 'confirmed')
    ctx.body = {
      signature,
      result
    }
  } catch (error) {
    handleError(ctx, error.message)
  }
}
