import { Connection, PublicKey } from '@solana/web3.js'
import * as multisig from '@sqds/multisig'
import { MULTISIG_ADDRESS_PUBKEY, SQUADS_PROGRAM_ID_V4_PUBKEY, SOLANA_RPC_URL } from './constants.js'

const connection = new Connection(SOLANA_RPC_URL, 'confirmed')

async function debugTransaction(transactionIndex) {
  console.log(`\n=== 调试交易 #${transactionIndex} ===`)

  try {
    // 获取 transaction 数据
    const [transactionPda] = multisig.getTransactionPda({
      multisigPda: MULTISIG_ADDRESS_PUBKEY,
      index: BigInt(transactionIndex),
      programId: SQUADS_PROGRAM_ID_V4_PUBKEY
    })

    console.log(`Transaction PDA: ${transactionPda.toBase58()}`)

    const transactionAccount = await connection.getAccountInfo(transactionPda)
    if (!transactionAccount) {
      console.log('交易账户不存在')
      return
    }

    console.log(`Transaction account data length: ${transactionAccount.data.length}`)

    if (transactionAccount.data.length < 150) {
      console.log('交易数据长度不足，可能不是转账交易')
      return
    }

    const vaultTransaction = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0]
    const { message, message: { instructions, accountKeys, addressTableLookups } } = vaultTransaction

    console.log(`Instructions count: ${instructions.length}`)
    console.log(`Account keys count: ${accountKeys.length}`)
    console.log(`Address table lookups count: ${addressTableLookups?.length || 0}`)

    // 构建完整的账户列表（包括地址表中的账户）
    let allAccountKeys = [...accountKeys]

    if (addressTableLookups && addressTableLookups.length > 0) {
      console.log('\nAddress Table Lookups:')
      for (const lookup of addressTableLookups) {
        console.log(`  Table: ${lookup.accountKey}`)
        console.log(`  Readonly indexes: ${JSON.stringify(lookup.readonlyIndexes)}`)
        console.log(`  Writable indexes: ${JSON.stringify(lookup.writableIndexes)}`)

        // 获取地址表账户信息
        try {
          const lookupTableAccount = await connection.getAddressLookupTable(new PublicKey(lookup.accountKey))
          if (lookupTableAccount.value) {
            const table = lookupTableAccount.value

            // 添加只读账户
            if (lookup.readonlyIndexes) {
              Object.values(lookup.readonlyIndexes).forEach(index => {
                if (index < table.state.addresses.length) {
                  allAccountKeys.push(table.state.addresses[index])
                  console.log(`    Readonly[${index}]: ${table.state.addresses[index].toBase58()}`)
                }
              })
            }

            // 添加可写账户
            if (lookup.writableIndexes) {
              Object.values(lookup.writableIndexes).forEach(index => {
                if (index < table.state.addresses.length) {
                  allAccountKeys.push(table.state.addresses[index])
                  console.log(`    Writable[${index}]: ${table.state.addresses[index].toBase58()}`)
                }
              })
            }
          }
        } catch (e) {
          console.log(`    无法获取地址表: ${e.message}`)
        }
      }
    }

    console.log(`\nTotal account keys (including lookups): ${allAccountKeys.length}`)
    console.log('All Account Keys:')
    allAccountKeys.forEach((key, index) => {
      console.log(`  [${index}] ${key.toBase58()}`)
    })

    for (let i = 0; i < instructions.length; i++) {
      const instruction = instructions[i]

      console.log(`\n--- Instruction ${i} ---`)
      console.log(`Program ID Index: ${instruction.programIdIndex}`)
      console.log(`Account Indexes: ${JSON.stringify(instruction.accountIndexes)}`)
      console.log(`Data length: ${instruction.data.length}`)
      console.log(`Data (hex): ${Buffer.from(Object.values(instruction.data)).toString('hex')}`)
      console.log(`Data (array): [${Object.values(instruction.data).join(', ')}]`)

      // 检查 programIdIndex 是否有效
      if (instruction.programIdIndex >= allAccountKeys.length) {
        console.log(`错误: programIdIndex ${instruction.programIdIndex} 超出 allAccountKeys 范围 (${allAccountKeys.length})`)
        continue
      }

      const programId = allAccountKeys[instruction.programIdIndex]
      console.log(`Program ID: ${programId.toBase58()}`)

      // 分析不同类型的指令
      const dataBuffer = Buffer.from(Object.values(instruction.data))

      if (dataBuffer.length >= 4) {
        const instructionType = dataBuffer.readUInt32LE(0)
        console.log(`Instruction type (first 4 bytes as uint32): ${instructionType}`)
      }

      if (dataBuffer.length >= 1) {
        console.log(`First byte: ${dataBuffer[0]}`)
      }

      // 尝试解析 SOL 转账 (System Program)
      if (programId.equals(new PublicKey('11111111111111111111111111111112'))) {
        console.log('这是 System Program 指令')
        if (dataBuffer.length >= 12) {
          try {
            const amount = dataBuffer.readBigUInt64LE(4)
            console.log(`SOL Amount (from offset 4): ${Number(amount) / 1e9} SOL`)
          } catch (e) {
            console.log(`无法读取 SOL 金额: ${e.message}`)
          }
        }

        // 尝试其他偏移量
        for (let offset = 0; offset <= Math.max(0, dataBuffer.length - 8); offset++) {
          try {
            const amount = dataBuffer.readBigUInt64LE(offset)
            if (amount > 0 && amount < 1e18) { // 合理的金额范围
              console.log(`可能的金额 (offset ${offset}): ${Number(amount) / 1e9} SOL`)
            }
          } catch (e) {
            // 忽略读取错误
          }
        }
      }

      // 账户信息
      const accountIndexes = Object.values(instruction.accountIndexes)
      console.log(`Account addresses:`)
      accountIndexes.forEach((index, i) => {
        if (index < allAccountKeys.length) {
          console.log(`  [${i}] ${allAccountKeys[index].toBase58()}`)
        }
      })
    }

  } catch (error) {
    console.error(`调试交易 #${transactionIndex} 失败:`, error.message)
  }
}

// 调试两个交易
async function main() {
  console.log('开始调试交易...')

  await debugTransaction(18) // 无法解析金额的交易
  await debugTransaction(43) // 可以解析金额的交易

  console.log('\n调试完成')
}

main().catch(console.error)
