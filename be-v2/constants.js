import { PublicKey } from '@solana/web3.js'

export const PORT = 3001
export const TX_MAX_RETRIES = 3
export const SOLANA_RPC_URL = 'https://api.mainnet-beta.solana.com'
export const MULTISIG_ADDRESS = 'HVx9YZh7ZNiNzBkRVyoGc3PxsznCJyv3PPz57fEKVuFr'
export const MULTISIG_ADDRESS_PUBKEY = new PublicKey(MULTISIG_ADDRESS)
export const SQUADS_PROGRAM_ID_V4_PUBKEY = new PublicKey('SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf')
export const SPL_TOKEN_PUBKEY = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')
