import axios from 'axios'
import * as multisig from '@sqds/multisig'
import { Connection, PublicKey, SystemProgram } from '@solana/web3.js'
import TOKENS from '../tokens.json' assert {type:'json'}
import { MULTISIG_ADDRESS_PUBKEY, SQUADS_PROGRAM_ID_V4_PUBKEY, SPL_TOKEN_PUBKEY, SOLANA_RPC_URL, TX_TIMEOUT, TX_MAX_RETRIES, PERMISSION_MASKS } from './constants.js'

const TOKEN_NAME_MAP = {}
for (const [symbol, { asset_id }] of Object.entries(TOKENS)) {
  TOKEN_NAME_MAP[asset_id] = symbol
}
export const getTokenName = mint => TOKEN_NAME_MAP[mint]
export const connection = new Connection(SOLANA_RPC_URL, 'confirmed')
export const handleError = (ctx, error) => {
  console.error(`error: ${error}`)
  ctx.status = 200
  ctx.body = { error }
}

export async function getPrice(chain) {
  // TODO 1: 假的
  let price = 0
  const tokenPrices = {
    'USDC': 1,
    'USDT': 1,
    'SOL': 100,
    'mSOL': 95,
    'BONK': 0.00001,
    'ETH': 2500,
    'BTC': 45000,
    'solusdc': 1,
    'solusdt': 1,
  }
  try {
    if (chain === 'SOL') {
      const { data } = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd', { timeout: 5000 })
      price = data?.solana?.usd || 0
    } else {
      price = tokenPrices[chain] || 0
    }
  } catch (error) {
    console.error(`get ${chain} price error: ${error.message}`)
  }
  return price
}


export async function getTokenAccounts(vaultPda) {
  const tokenAccounts = []
  try {
    const tokenAccountsResponse = await connection.getParsedTokenAccountsByOwner(vaultPda, { programId: SPL_TOKEN_PUBKEY})
    for (const { pubkey, account: { data: { parsed: { info: { mint, tokenAmount: { uiAmount, amount, decimals, uiAmountString }}}}}} of tokenAccountsResponse.value) {
      // 只包含有余额的 Token 账户
      if (uiAmount > 0) {
        const tokenPrice = await getPrice(getTokenName(mint))
        tokenAccounts.push({
          address: pubkey.toBase58(),
          mint: mint,
          name: getTokenName(mint),
          balance: parseInt(amount),
          decimals: decimals,
          uiAmount: uiAmount,
          uiAmountString: uiAmountString,
          price: tokenPrice,
          value: uiAmount * tokenPrice
        })
      }
    }
  } catch (error) {
    console.error(`get token account error:`, error.message)
  }
  return tokenAccounts
}

export const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key])
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null
}

export const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    {
      skipPreflight: true,
      preflightCommitment: 'confirmed',
      maxRetries: TX_MAX_RETRIES,
      ...options
    }
  )
  return signature
}

export async function parseTransfer(programId, instruction, message) {
  if (programId && programId.equals(SPL_TOKEN_PUBKEY)) {
    console.log(`i: == 6-1`)
    // SPL Token Program 转账
    const accountIndexes = Object.values(instruction.accountIndexes)
    const source = message.accountKeys[accountIndexes[0]].toBase58()
    const mint = message.accountKeys[accountIndexes[1]].toBase58()
    const destination = message.accountKeys[accountIndexes[2]].toBase58()
    console.log(`i: == 6-2`)
    const dataBuffer = Buffer.from(Object.values(instruction.data))

    // 检查 buffer 长度是否足够读取 amount (需要至少10字节: 1字节指令 + 8字节amount + 1字节decimals)
    if (dataBuffer.length < 10) {
      console.log(`i: == 6-2-error: SPL Token buffer too short: ${dataBuffer.length} bytes`)
      return {
        chain: 'unknown',
        amount: 0,
        from: source,
        to: destination
      }
    }

    const amount = dataBuffer.readBigUInt64LE(1)
    const decimals = dataBuffer.readUInt8(9)

    const { value: { data: { parsed: { info: { owner: from } } } } } = await connection.getParsedAccountInfo(new PublicKey(source))
    const { value: { data: { parsed: { info: { owner: to } } } } } = await connection.getParsedAccountInfo(new PublicKey(destination))
    console.log(`i: == 6-3`)
    return {
      chain: getTokenName(mint),
      amount: Number(amount) / Math.pow(10, decimals),
      from,
      to
    }
  } else {
    console.log(`i: == 6-4`)
    // System Program 转账 (SOL)
    const accountIndexes = Object.values(instruction.accountIndexes)
    console.log(`i: == 6-5`)
    const dataBuffer = Buffer.from(Object.values(instruction.data))
    console.log(`i: == 6-6, buffer length: ${dataBuffer.length}`)

    // 检查 buffer 长度是否足够读取 amount (需要至少12字节: 4字节指令类型 + 8字节amount)
    if (dataBuffer.length < 12) {
      console.log(`i: == 6-6-error: System Program buffer too short: ${dataBuffer.length} bytes, expected at least 12 bytes`)
      return {
        chain: 'sol',
        amount: 0,
        from: message.accountKeys[accountIndexes[0]].toBase58(),
        to: message.accountKeys[accountIndexes[1]].toBase58(),
      }
    }

    const rawAmount = dataBuffer.readBigUInt64LE(4)
    console.log(`i: == 6-7`)
    return {
      chain: 'sol',
      amount: Number(rawAmount) / 1e9,
      from: message.accountKeys[accountIndexes[0]].toBase58(),
      to: message.accountKeys[accountIndexes[1]].toBase58(),
    }
  }
}

export function parsePermissions(mask) {
  const permissions = []
  if ((mask & PERMISSION_MASKS.PROPOSER) !== 0) permissions.push('Proposer')
  if ((mask & PERMISSION_MASKS.VOTER) !== 0) permissions.push('Voter')
  if ((mask & PERMISSION_MASKS.EXECUTOR) !== 0) permissions.push('Executor')
  return permissions
}

export function calculateAssetWeights(assets) {
  const totalValue = assets.reduce((sum, asset) => sum + asset.value, 0)

  return assets.map(asset => ({
    ...asset,
    weight: totalValue > 0 ? asset.value / totalValue : 0
  }))
}
