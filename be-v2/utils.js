import axios from 'axios'
import * as multisig from '@sqds/multisig'
import { Connection, PublicKey, SystemProgram } from '@solana/web3.js'
import TOKENS from '../tokens.json' assert {type:'json'}
import { MULTISIG_ADDRESS_PUBKEY, SQUADS_PROGRAM_ID_V4_PUBKEY, SPL_TOKEN_PUBKEY, SOLANA_RPC_URL, TX_TIMEOUT, TX_MAX_RETRIES, PERMISSION_MASKS } from './constants.js'

const TOKEN_NAME_MAP = {}
for (const [symbol, { asset_id }] of Object.entries(TOKENS)) {
  TOKEN_NAME_MAP[asset_id] = symbol
}
export const getTokenName = mint => TOKEN_NAME_MAP[mint]
export const connection = new Connection(SOLANA_RPC_URL, 'confirmed')
export const handleError = (ctx, error) => {
  console.error(`error: ${error}`)
  ctx.status = 200
  ctx.body = { error }
}

export async function getPrice(chain) {
  // TODO 1: 假的
  let price = 0
  const tokenPrices = {
    'USDC': 1,
    'USDT': 1,
    'SOL': 100,
    'mSOL': 95,
    'BONK': 0.00001,
    'ETH': 2500,
    'BTC': 45000,
    'solusdc': 1,
    'solusdt': 1,
  }
  try {
    if (chain === 'SOL') {
      const { data } = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd', { timeout: 5000 })
      price = data?.solana?.usd || 0
    } else {
      price = tokenPrices[chain] || 0
    }
  } catch (error) {
    console.error(`get ${chain} price error: ${error.message}`)
  }
  return price
}


export async function getTokenAccounts(vaultPda) {
  const tokenAccounts = []
  try {
    const tokenAccountsResponse = await connection.getParsedTokenAccountsByOwner(vaultPda, { programId: SPL_TOKEN_PUBKEY})
    for (const { pubkey, account: { data: { parsed: { info: { mint, tokenAmount: { uiAmount, amount, decimals, uiAmountString }}}}}} of tokenAccountsResponse.value) {
      // 只包含有余额的 Token 账户
      if (uiAmount > 0) {
        const tokenPrice = await getPrice(getTokenName(mint))
        tokenAccounts.push({
          address: pubkey.toBase58(),
          mint: mint,
          name: getTokenName(mint),
          balance: parseInt(amount),
          decimals: decimals,
          uiAmount: uiAmount,
          uiAmountString: uiAmountString,
          price: tokenPrice,
          value: uiAmount * tokenPrice
        })
      }
    }
  } catch (error) {
    console.error(`get token account error:`, error.message)
  }
  return tokenAccounts
}

export const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key])
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null
}

export const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    {
      skipPreflight: true,
      preflightCommitment: 'confirmed',
      maxRetries: TX_MAX_RETRIES,
      ...options
    }
  )
  return signature
}

export async function parseTransfer(programId, instruction, message) {
  try {
    // 构建完整的账户列表（包括地址表查找）
    let allAccountKeys = [...message.accountKeys]
    if (message.addressTableLookups && message.addressTableLookups.length > 0) {
      for (const lookup of message.addressTableLookups) {
        try {
          const lookupTableAccount = await connection.getAddressLookupTable(new PublicKey(lookup.accountKey))
          if (lookupTableAccount.value) {
            const table = lookupTableAccount.value
            if (lookup.readonlyIndexes) {
              Object.values(lookup.readonlyIndexes).forEach(index => {
                if (index < table.state.addresses.length) {
                  allAccountKeys.push(table.state.addresses[index])
                }
              })
            }
          }
        } catch (e) {
          // 忽略地址表获取失败
        }
      }
    }

    if (programId && programId.equals(SPL_TOKEN_PUBKEY)) {
      // SPL Token Program 转账
      const accountIndexes = Object.values(instruction.accountIndexes)
      const dataBuffer = Buffer.from(Object.values(instruction.data))

      // 支持不同的 SPL Token 指令类型
      let amount = 0, decimals = 6
      if (dataBuffer.length >= 10 && dataBuffer[0] === 12) {
        // TransferChecked 指令
        amount = dataBuffer.readBigUInt64LE(1)
        decimals = dataBuffer.readUInt8(9)
      } else if (dataBuffer.length >= 9 && dataBuffer[0] === 3) {
        // Transfer 指令
        amount = dataBuffer.readBigUInt64LE(1)
      }

      if (amount === 0) return { chain: 'unknown', amount: 0, from: 'unknown', to: 'unknown' }

      const source = allAccountKeys[accountIndexes[0]].toBase58()
      const mint = allAccountKeys[accountIndexes[1]].toBase58()
      const destination = allAccountKeys[accountIndexes[2]].toBase58()

      const { value: { data: { parsed: { info: { owner: from } } } } } = await connection.getParsedAccountInfo(new PublicKey(source))
      const { value: { data: { parsed: { info: { owner: to } } } } } = await connection.getParsedAccountInfo(new PublicKey(destination))

      return {
        chain: getTokenName(mint),
        amount: Number(amount) / Math.pow(10, decimals),
        from,
        to
      }
    } else {
      // System Program 转账 (SOL)
      const accountIndexes = Object.values(instruction.accountIndexes)
      const dataBuffer = Buffer.from(Object.values(instruction.data))

      if (dataBuffer.length < 12) {
        return { chain: 'sol', amount: 0, from: 'unknown', to: 'unknown' }
      }

      const rawAmount = dataBuffer.readBigUInt64LE(4)

      return {
        chain: 'sol',
        amount: Number(rawAmount) / 1e9,
        from: allAccountKeys[accountIndexes[0]].toBase58(),
        to: allAccountKeys[accountIndexes[1]].toBase58(),
      }
    }
  } catch (error) {
    console.log(`parseTransfer error: ${error.message}`)
    return { chain: 'unknown', amount: 0, from: 'unknown', to: 'unknown' }
  }
}

export function parsePermissions(mask) {
  const permissions = []
  if ((mask & PERMISSION_MASKS.PROPOSER) !== 0) permissions.push('Proposer')
  if ((mask & PERMISSION_MASKS.VOTER) !== 0) permissions.push('Voter')
  if ((mask & PERMISSION_MASKS.EXECUTOR) !== 0) permissions.push('Executor')
  return permissions
}

export function calculateAssetWeights(assets) {
  const totalValue = assets.reduce((sum, asset) => sum + asset.value, 0)

  return assets.map(asset => ({
    ...asset,
    weight: totalValue > 0 ? asset.value / totalValue : 0
  }))
}
