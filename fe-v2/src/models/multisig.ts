import { useState, useCallback, useEffect } from 'react'
import { useWallet } from '@solana/wallet-adapter-react'
import Api from '@/services/api'

let isRequesting = false
let requestPromise: Promise<any> | null = null

export default function useMultisigModel() {
  const { publicKey } = useWallet()
  const [multisigs, setMultisigs] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userBalance, setUserBalance] = useState<number>(0)
  const [solPrice, setSolPrice] = useState<number>(100)
  const [totalVaultValue, setTotalVaultValue] = useState<number>(0)

  const refreshMultisigs = useCallback(async (forceRefresh = false) => {
    if (isRequesting && !forceRefresh && requestPromise) {
      try {
        const result = await requestPromise
        setMultisigs([result])
        setSolPrice(result.solPrice)
        setTotalVaultValue(result.vault.totalValue)
        setError(null)
      } catch (err: any) {
        setError(err.message || '获取多签信息失败')
      }
      return
    }

    // 有数据且不是强制刷新，直接返回
    if (!forceRefresh && multisigs.length > 0) return

    try {
      setLoading(true)
      setError(null)
      isRequesting = true

      requestPromise = Api.getMultisigs()
      const multisigResult = await requestPromise

      setMultisigs([multisigResult])
      setSolPrice(multisigResult.solPrice)
      setTotalVaultValue(multisigResult.vault.totalValue)
    } catch (err: any) {
      console.error('获取多签信息失败:', err)
      setError(err.message || '获取多签信息失败')
      setMultisigs([])
      setTotalVaultValue(0)
    } finally {
      setLoading(false)
      isRequesting = false
      requestPromise = null
    }
  }, [multisigs.length])

  const refreshUserBalance = useCallback(async () => {
    if (!publicKey) {
      setUserBalance(0)
      return
    }

    try {
      const result = await Api.getUserBalance(publicKey.toBase58())
      setUserBalance(result.balance / 1e9) // 转换为 SOL
    } catch (err: any) {
      console.error('获取用户余额失败:', err)
      setUserBalance(0)
    }
  }, [publicKey])

  // 当钱包连接状态变化时，刷新用户余额
  useEffect(() => {
    refreshUserBalance()
  }, [refreshUserBalance])

  return {
    multisigs,
    currentMultisig: multisigs[0],
    userBalance, // Phantom 钱包的 SOL 余额
    totalVaultValue, // 多签金库总价值（美元）
    solPrice,
    loading,
    error,
    refreshMultisigs,
    refreshUserBalance,
    totalBalance: userBalance,
  }
}
